#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script spécialisé pour traiter le débat Macron-Le Pen
"""

import re

def process_debate_transcript():
    """Traite spécifiquement le fichier de débat Macron-Le Pen"""
    
    input_file = "1 Débat Macron LePn.txt"
    
    print("🎯 Traitement du débat Macron-Le Pen...")
    
    # Lire le fichier
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        with open(input_file, 'r', encoding='latin-1') as f:
            content = f.read()
    
    lines = content.split('\n')
    
    # Supprimer les timestamps et lignes vides
    cleaned_lines = []
    for line in lines:
        line = line.strip()
        # Ignorer les timestamps (format MM:SS)
        if line and not re.match(r'^\d{1,2}:\d{2}$', line):
            cleaned_lines.append(line)
    
    # Rejoindre en un seul texte
    full_text = ' '.join(cleaned_lines)
    
    # Nettoyer les bruits
    full_text = re.sub(r'\[Musique\]', '', full_text)
    full_text = re.sub(r'\s+', ' ', full_text)
    
    # Identifier les segments par locuteur
    segments = []
    
    # Diviser en phrases
    sentences = re.split(r'[.!?]+', full_text)
    current_speaker = "MODERATEUR"
    current_text = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
        
        # Détecter les changements de locuteur
        new_speaker = None
        if re.search(r'\b(Marine Le Pen|Le Pen)\b', sentence, re.IGNORECASE):
            new_speaker = "MARINE_LE_PEN"
        elif re.search(r'\b(Emmanuel Macron|Macron)\b', sentence, re.IGNORECASE):
            new_speaker = "EMMANUEL_MACRON"
        elif re.search(r'\b(Gilles|Bouleau|Léa)\b', sentence, re.IGNORECASE):
            new_speaker = "MODERATEUR"
        
        # Si changement de locuteur
        if new_speaker and new_speaker != current_speaker:
            if current_text.strip():
                segments.append((current_speaker, current_text.strip()))
            current_speaker = new_speaker
            current_text = sentence
        else:
            current_text += " " + sentence
    
    # Ajouter le dernier segment
    if current_text.strip():
        segments.append((current_speaker, current_text.strip()))
    
    # Sauvegarder pour AntConc
    with open("debat_antconc.txt", 'w', encoding='utf-8') as f:
        for i, (speaker, text) in enumerate(segments, 1):
            f.write(f"<SEG_{i:03d}><{speaker}> {text}\n")
    
    # Sauvegarder par locuteur
    with open("debat_par_locuteur.txt", 'w', encoding='utf-8') as f:
        f.write("=== DÉBAT MACRON - LE PEN ===\n\n")
        for speaker, text in segments:
            f.write(f"[{speaker}] {text}\n\n")
    
    # Statistiques
    stats = {}
    for speaker, text in segments:
        if speaker not in stats:
            stats[speaker] = {'segments': 0, 'mots': 0}
        stats[speaker]['segments'] += 1
        stats[speaker]['mots'] += len(text.split())
    
    print("\n📊 STATISTIQUES:")
    print("-" * 40)
    for speaker, data in stats.items():
        print(f"{speaker}:")
        print(f"  - Segments: {data['segments']}")
        print(f"  - Mots: {data['mots']}")
        print(f"  - Mots/segment: {data['mots']/data['segments']:.1f}")
        print()
    
    print(f"✅ Traitement terminé!")
    print(f"📁 Fichiers créés:")
    print(f"   - debat_antconc.txt (pour AntConc)")
    print(f"   - debat_par_locuteur.txt (lecture)")
    print(f"📈 {len(segments)} segments traités")

if __name__ == "__main__":
    process_debate_transcript()
