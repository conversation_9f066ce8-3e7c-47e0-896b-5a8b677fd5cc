#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de traitement de transcriptions YouTube
Auteur: Assistant IA
Date: 2025-08-27

Ce script traite les transcriptions YouTube pour :
- Nettoyer les timestamps et bruits typographiques
- Identifier et annoter les locuteurs
- Structurer le texte pour l'analyse avec AntConc
"""

import re
import os
from typing import List, Tuple, Dict
from collections import defaultdict

class TranscriptProcessor:
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.raw_text = ""
        self.cleaned_segments = []
        self.speakers = {}
        
    def load_transcript(self) -> None:
        """Charge le fichier de transcription"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.raw_text = f.read()
            print(f"✓ Fichier chargé: {self.input_file}")
        except FileNotFoundError:
            print(f"❌ Erreur: Fichier '{self.input_file}' non trouvé")
            return
        except UnicodeDecodeError:
            # Essayer avec d'autres encodages
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(self.input_file, 'r', encoding=encoding) as f:
                        self.raw_text = f.read()
                    print(f"✓ Fichier chargé avec encodage {encoding}: {self.input_file}")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                print(f"❌ Erreur: Impossible de décoder le fichier '{self.input_file}'")
                return

    def clean_timestamps(self) -> str:
        """Supprime les timestamps du format MM:SS ou H:MM:SS"""
        # Pattern pour les timestamps (0:03, 1:28, 12:34, etc.)
        timestamp_pattern = r'^\d{1,2}:\d{2}$'
        
        lines = self.raw_text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # Ignorer les lignes vides et les timestamps
            if line and not re.match(timestamp_pattern, line):
                cleaned_lines.append(line)
        
        cleaned_text = ' '.join(cleaned_lines)
        print(f"✓ Timestamps supprimés ({len(lines) - len(cleaned_lines)} lignes supprimées)")
        return cleaned_text

    def remove_noise(self, text: str) -> str:
        """Supprime les bruits typographiques et normalise le texte"""
        # Supprimer les marqueurs de musique et autres bruits
        text = re.sub(r'\[Musique\]', '', text)
        text = re.sub(r'\[.*?\]', '', text)  # Autres marqueurs entre crochets
        text = re.sub(r'\(.*?\)', '', text)  # Marqueurs entre parenthèses
        
        # Normaliser les espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        # Supprimer les répétitions de mots (doublons simples)
        words = text.split()
        cleaned_words = []
        prev_word = ""
        
        for word in words:
            if word.lower() != prev_word.lower():
                cleaned_words.append(word)
            prev_word = word
        
        cleaned_text = ' '.join(cleaned_words)
        print("✓ Bruits typographiques supprimés")
        return cleaned_text

    def identify_speakers(self, text: str) -> List[Tuple[str, str]]:
        """Identifie les locuteurs et segmente le texte"""
        # Patterns pour identifier les locuteurs
        speaker_patterns = [
            r'\b(Marine Le Pen|Le Pen)\b',
            r'\b(Emmanuel Macron|Macron)\b',
            r'\b(Gilles Bouleau|Gilles)\b',
            r'\b(Léa)\b',
            r'\bjournaliste\b',
            r'\bprésentateur\b',
            r'\bmodérateur\b'
        ]
        
        segments = []
        current_speaker = "INCONNU"
        
        # Diviser le texte en phrases
        sentences = re.split(r'[.!?]+', text)
        current_segment = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # Chercher un nom de locuteur dans la phrase
            speaker_found = None
            for pattern in speaker_patterns:
                if re.search(pattern, sentence, re.IGNORECASE):
                    if "Marine" in sentence or "Le Pen" in sentence:
                        speaker_found = "MARINE_LE_PEN"
                    elif "Macron" in sentence or "Emmanuel" in sentence:
                        speaker_found = "EMMANUEL_MACRON"
                    elif "Gilles" in sentence:
                        speaker_found = "GILLES_BOULEAU"
                    elif "Léa" in sentence:
                        speaker_found = "LEA"
                    else:
                        speaker_found = "JOURNALISTE"
                    break
            
            # Si nouveau locuteur détecté, sauvegarder le segment précédent
            if speaker_found and speaker_found != current_speaker:
                if current_segment.strip():
                    segments.append((current_speaker, current_segment.strip()))
                current_speaker = speaker_found
                current_segment = sentence
            else:
                current_segment += " " + sentence
        
        # Ajouter le dernier segment
        if current_segment.strip():
            segments.append((current_speaker, current_segment.strip()))
        
        print(f"✓ {len(segments)} segments identifiés")
        return segments

    def structure_for_antconc(self, segments: List[Tuple[str, str]]) -> str:
        """Structure le texte pour l'analyse avec AntConc"""
        structured_lines = []
        
        for i, (speaker, text) in enumerate(segments, 1):
            # Format: <SEGMENT_ID><SPEAKER> texte
            line = f"<SEG_{i:03d}><{speaker}> {text}"
            structured_lines.append(line)
        
        structured_text = '\n'.join(structured_lines)
        print("✓ Texte structuré pour AntConc")
        return structured_text

    def generate_speaker_stats(self, segments: List[Tuple[str, str]]) -> Dict:
        """Génère des statistiques sur les locuteurs"""
        stats = defaultdict(lambda: {'segments': 0, 'words': 0})
        
        for speaker, text in segments:
            stats[speaker]['segments'] += 1
            stats[speaker]['words'] += len(text.split())
        
        return dict(stats)

    def save_results(self, structured_text: str, segments: List[Tuple[str, str]]) -> None:
        """Sauvegarde les résultats dans différents formats"""
        base_name = os.path.splitext(self.input_file)[0]
        
        # 1. Fichier structuré pour AntConc
        antconc_file = f"{base_name}_antconc.txt"
        with open(antconc_file, 'w', encoding='utf-8') as f:
            f.write(structured_text)
        print(f"✓ Fichier AntConc sauvegardé: {antconc_file}")
        
        # 2. Fichier par locuteur
        speakers_file = f"{base_name}_speakers.txt"
        with open(speakers_file, 'w', encoding='utf-8') as f:
            f.write("=== ANALYSE PAR LOCUTEUR ===\n\n")
            current_speaker = ""
            for speaker, text in segments:
                if speaker != current_speaker:
                    f.write(f"\n--- {speaker} ---\n")
                    current_speaker = speaker
                f.write(f"{text}\n\n")
        print(f"✓ Fichier par locuteur sauvegardé: {speakers_file}")
        
        # 3. Statistiques
        stats_file = f"{base_name}_stats.txt"
        stats = self.generate_speaker_stats(segments)
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("=== STATISTIQUES DES LOCUTEURS ===\n\n")
            for speaker, data in stats.items():
                f.write(f"{speaker}:\n")
                f.write(f"  - Segments: {data['segments']}\n")
                f.write(f"  - Mots: {data['words']}\n")
                f.write(f"  - Mots/segment: {data['words']/data['segments']:.1f}\n\n")
        print(f"✓ Statistiques sauvegardées: {stats_file}")

    def process(self) -> None:
        """Lance le traitement complet"""
        print("🚀 Début du traitement de la transcription...")
        print("=" * 50)
        
        # Étape 1: Charger le fichier
        self.load_transcript()
        if not self.raw_text:
            return
        
        # Étape 2: Nettoyer les timestamps
        cleaned_text = self.clean_timestamps()
        
        # Étape 3: Supprimer les bruits
        cleaned_text = self.remove_noise(cleaned_text)
        
        # Étape 4: Identifier les locuteurs et segmenter
        segments = self.identify_speakers(cleaned_text)
        
        # Étape 5: Structurer pour AntConc
        structured_text = self.structure_for_antconc(segments)
        
        # Étape 6: Sauvegarder les résultats
        self.save_results(structured_text, segments)
        
        print("=" * 50)
        print("✅ Traitement terminé avec succès!")
        print(f"📊 {len(segments)} segments traités")

def main():
    """Fonction principale"""
    print("🎥 PROCESSEUR DE TRANSCRIPTION YOUTUBE")
    print("=" * 50)
    
    # Demander le fichier d'entrée
    input_file = input("Nom du fichier de transcription (ex: transcript.txt): ").strip()
    
    if not input_file:
        print("❌ Aucun fichier spécifié")
        return
    
    if not os.path.exists(input_file):
        print(f"❌ Fichier '{input_file}' non trouvé")
        return
    
    # Traiter le fichier
    processor = TranscriptProcessor(input_file)
    processor.process()

if __name__ == "__main__":
    main()
